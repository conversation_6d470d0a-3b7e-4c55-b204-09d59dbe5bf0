<template>
  <div class="app-container">
    <div class="app-content">
      <div class="el-page-header">
        <div class="el-page-header__content">
          {{ $t("permissionUser.userManagement") }}
        </div>
      </div>
      <el-divider />
      <el-button type="primary" @click="handleAddUser" icon="el-icon-plus">{{
        $t("permissionUser.addUser")
      }}</el-button>

      <el-table :data="usersList" style="width: 100%; margin-top: 30px" :header-cell-style="{
        background: '#F7FBFF',
        height: '52px',
      }">
        <el-table-column align="center" :label="$t('permissionUser.avatar')" width="65">
          <el-image slot-scope="scope" style="width: 50px; height: 50px"
            :src="`${baseUrl}/media/avatar/${scope.row.userId}.png?time=${currentTime}`"></el-image>
        </el-table-column>
        <el-table-column align="center" :label="$t('permissionUser.userId')" width="100"><template slot-scope="scope">
            {{ scope.row.userId }}
          </template></el-table-column>
        <el-table-column align="center" :label="$t('permissionUser.userName')" width="150">
          <template slot-scope="scope">
            {{ scope.row.userName }}
          </template>
        </el-table-column>
        <el-table-column align="center" :label="$t('permissionUser.nickName')" width="150">
          <template slot-scope="scope">
            {{ scope.row.nickName }}
          </template>
        </el-table-column>
        <el-table-column align="center" :label="$t('permissionUser.userRole')" width="150">
          <template slot-scope="scope">
            <el-tag :type="scope.row.roleKey.includes('admin') ? 'success' : ''">{{ scope.row.roleName }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column align="center" :label="$t('permissionUser.phone')" min-width="180">
          <template slot-scope="scope"> {{ scope.row.phone }} </template>
        </el-table-column>
        <el-table-column align="center" :label="$t('permissionUser.email')" min-width="180">
          <template slot-scope="scope">
            {{ scope.row.email }}
          </template>
        </el-table-column>
        <!-- <el-table-column
          align="center"
          label="所属项目"
          fixed="right"
          min-width="100"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.projectList">
              <span v-if="scope.row.projectList.length > 0">{{
                scope.row.projectList[0].projectName
              }}</span>
              <el-tooltip placement="top">
                <div slot="content">
                  <div v-for="p in scope.row.projectList" :key="p.projectId">
                    {{ p.projectName }}
                  </div>
                </div>
                <el-button
                  v-show="scope.row.projectList.length > 1"
                  icon="el-icon-more"
                  type="text"
                ></el-button>
              </el-tooltip>
            </div>

            <el-button
              type="text"
              style="margin-left: 5px"
              @click="getUserProjects(scope.row)"
            >
              <i class="el-icon-setting"></i>配置/查看</el-button
            >
          </template>
        </el-table-column> -->
        <el-table-column align="center" :label="$t('permissionUser.isEnabled')" fixed="right" min-width="88">
          <template slot-scope="scope">
            <el-switch @change="changeSwitch($event, scope.row.userId)" v-model="scope.row.isEnabled"
              active-color="#13ce66" inactive-color="#ff4949">
            </el-switch>
          </template>
        </el-table-column>

        <el-table-column align="center" :label="$t('permissionUser.operator')" fixed="right" width="238">
          <template slot-scope="scope">
            <el-button type="text" icon="el-icon-edit-outline" @click="handleEdit(scope.row)">{{ $t("permissionUser.edit")
            }}</el-button>
            <el-button type="text" icon="el-icon-magic-stick" :disabled="scope.row.disabled"
              @click="resetPassword(scope.row.userId)">{{ $t("permissionUser.resetPwd") }}</el-button>
            <el-button type="text" style="color: #f56c6c" icon="el-icon-delete" :disabled="scope.row.disabled"
              @click="handleDelete(scope.row)">{{ $t("permissionUser.delete") }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页器 -->
      <pagination style="margin-top: 0; padding: 10px 20px" :total="total" :page.sync="listQuery.page"
        :limit.sync="listQuery.limit" @pagination="pageSwitch" />
    </div>

    <!-- 新增/编辑用户弹窗 -->
    <el-dialog :visible.sync="dialogVisible" @close="closeUserDialog" :close-on-click-modal="false" :title="dialogType === 'edit'
        ? $t('permissionUser.editUser')
        : $t('permissionUser.addUser')
      ">
      <el-form ref="userForm" :model="user" :rules="rules" label-width="100px" label-position="right">
        <el-form-item :label="$t('permissionUser.avatar')">
          <el-upload class="avatar-uploader" action="" accept=".jpg,.jpeg,.png,.gif,.webp" :show-file-list="false"
            :auto-upload="false" :on-change="fileChang">
            <template v-if="imageUrl">
              <img :src="imageUrl" class="avatar" />
              <div class="upload-handle" @click.stop>
                <div class="handle-icon" @click="editImg">
                  <i class="el-icon-edit-outline" style="padding-bottom: 4px"></i>
                  <span>{{ $t("uploadImage.change") }}</span>
                </div>
                <div class="handle-icon" @click="imgViewVisible = true">
                  <i class="el-icon-zoom-in" style="padding-bottom: 4px"></i>
                  <span>{{ $t("uploadImage.check") }}</span>
                </div>
              </div>
            </template>

            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-form-item :label="$t('permissionUser.userName')" prop="userName">
          <el-input ref="userName" v-model="user.userName" maxlength="15"
            :placeholder="$t('permissionUser.enterUserName')" />
        </el-form-item>
        <el-form-item :label="$t('permissionUser.nickName')">
          <el-input v-model="user.nickName" maxlength="15" :placeholder="$t('permissionUser.enterNickName')" />
        </el-form-item>
        <el-form-item :label="$t('permissionUser.userRole')" prop="roleId">
          <el-select v-model="user.roleId" :placeholder="$t('permissionUser.selectRole')">
            <el-option v-for="(item, index) in rolesList" :key="index" :label="item.roleName" :value="item.roleId">
            </el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="所属项目">
          <el-select
            v-model="projectIds"
            filterable
            multiple
            placeholder="请选择所属项目(可搜索)"
            @change="getSelectProjects"
            style="width: 100%"
          >
            <el-option
              v-for="item in projectList"
              :key="item.projectId"
              :label="item.projectName"
              :value="item.projectId"
            >
            </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item :label="$t('permissionUser.phone')" prop="phone">
          <el-input v-model="user.phone" :placeholder="$t('permissionUser.enterPhone')" />
        </el-form-item>
        <el-form-item :label="$t('permissionUser.email')" prop="email">
          <el-input v-model="user.email" :placeholder="$t('permissionUser.enterEmail')" />
        </el-form-item>
        <el-form-item :label="$t('permissionUser.isEnabled')">
          <el-switch v-model="user.isEnabled" active-color="#13ce66" inactive-color="#ff4949">
          </el-switch>
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button type="danger" @click="closeUserDialog">{{
          $t("permissionUser.cancel")
        }}</el-button>
        <el-button type="primary" @click="confirmUser">{{
          $t("permissionUser.ok")
        }}</el-button>
      </div>
    </el-dialog>
    <!-- 查看头像 -->
    <el-image-viewer style="z-index: 999999" v-if="imgViewVisible" :on-close="closeImgViewer" :url-list="[imageUrl]" />

    <!-- 配置用户所属的项目弹窗 -->
    <set-user-project :setProjectModal="dialogSetProjects" :useProjectlist="useProjectlist"></set-user-project>
    <!-- <el-dialog
      title="配置所属项目"
      :append-to-body="true"
      :visible.sync="dialogSetProjects"
      label-width="80px"
      label-position="left"
      @close="closeSetProject"
    >
      <el-checkbox
        :indeterminate="isIndeterminate"
        v-model="checkAllProject"
        @change="handleCheckAllProject"
        >全选</el-checkbox
      >
      <div style="margin: 15px 0"></div>
      <el-checkbox-group v-model="projectIds" @change="handleCheckedProject">
        <el-checkbox
          v-for="item in projectList"
          :label="item.projectId"
          :key="item.projectId"
          >{{ item.projectName }}</el-checkbox
        >
      </el-checkbox-group>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeSetProject">取 消</el-button>
        <el-button type="primary" @click="setUserProjects">确 定</el-button>
      </div>
    </el-dialog> -->
  </div>
</template>

<script>
import Pagination from "@/components/Pagination/index.vue";
import { deepClone } from "@/utils";
import {
  getAllUser,
  createUser,
  editUser,
  isEnabledUser,
  updatePassword,
  deleteUser,
  getUserProjectList,
} from "@/api/permission-users";
import { getRoles } from "@/api/role";
import { uploadAvatar } from "@/api/upload";
import SetUserProject from "./components/SetUserProject.vue";

const defaultUser = {
  userName: "",
  nickName: "",
  avatar: "",
  roleId: "",
  // roleName: "",
  projectList: [],
  phone: "",
  email: "",
  isEnabled: true,
};

export default {
  components: {
    Pagination,
    SetUserProject,
    "el-image-viewer": () =>
      import("element-ui/packages/image/src/image-viewer"),
  },
  data() {
    return {
      user: Object.assign({}, defaultUser), //保存用户信息
      usersList: [], //保存用户列表
      rolesList: [], //保存角色选项列表
      projectList: [], //保存所有项目列表
      dialogVisible: false, //新增/编辑项目弹窗开关
      dialogType: "new",
      uploadFile: "", //保存上传的文件
      baseUrl: process.env.VUE_APP_BASE_API, //图片基础路径
      imageUrl: "", //保存用户头像
      imgViewVisible: false, //查看头像
      projectIds: [], // 用户所属项目id

      dialogSetProjects: false, //配置用户所属项目的弹窗
      useProjectlist: [], //用户所属项目
      checkAllProject: false, //是否全选所有项目
      isIndeterminate: false,

      rules: {
        userName: [
          {
            required: true,
            message: this.$t("permissionUser.enterUserName"),
            trigger: "blur",
          },
          {
            min: 3,
            max: 15,
            message: this.$t("permissionUser.characterLength"),
            trigger: "blur",
          },
        ],
        roleId: [
          {
            required: true,
            message: this.$t("permissionUser.selectRole"),
            trigger: "change",
          },
        ],
        phone: [
          {
            pattern: /^((\+|00)86)?1\d{10}$/,
            message: this.$t("permissionUser.phoneRegTips"),
            trigger: "blur",
          },
        ],
        email: [
          {
            pattern: /^\w{3,}(\.\w+)*@[A-z0-9]+(\.[A-z]{2,5}){1,2}$/,
            message: this.$t("permissionUser.emailRegTips"),
            trigger: "blur",
          },
        ],
      },

      total: 0,
      listQuery: {
        page: 1,
        limit: 10,
      },
      currentTime: Date.now(), //保存当前时间用于获取最新头像
    };
  },

  mounted() {
    this.getUsersList();
  },
  methods: {
    // 查看图片
    closeImgViewer() {
      this.imgViewVisible = false;
    },
    // 更换图片
    editImg() {
      const dom = document.querySelector(`.el-upload__input`);
      dom && dom.dispatchEvent(new MouseEvent("click"));
    },

    // 获取所有用户列表
    async getUsersList() {
      let params = {
        pageNum: this.listQuery.page,
        pageSize: this.listQuery.limit,
      };
      const { code, data, dataLen } = await getAllUser(params);
      if (code === 200) {
        this.usersList = data;
        this.total = dataLen;
        this.currentTime = Date.now();
      }
    },
    // 分页器页码改变获取角色数据
    pageSwitch(value) {
      this.getUsersList();
    },

    // 全选所有项目
    handleCheckAllProject(val) {
      // console.log(val);
      if (!val) {
        this.projectIds = [];
      } else {
        this.projectList.forEach((item) => {
          this.projectIds.push(item.projectId);
        });
      }
      this.isIndeterminate = false;
    },
    // 选中的项目
    handleCheckedProject(value) {
      // console.log(value);
      this.projectIds = value;
      let checkedCount = value.length;
      this.checkAllProject = checkedCount === this.projectList.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.projectList.length;
    },

    // 获取用户所属的所有项目
    async getUserProjects(row) {
      // console.log(row);
      // let data = await getUserProjectList({ projectId: 9 });
      // this.user = deepClone(row);
      // this.projectIds = [];
      // if (row.projectList) {
      //   row.projectList.forEach((p) => {
      //     this.projectIds.push(p.projectId);
      //   });
      // }
      this.useProjectlist = row.projectInfoList;
      this.dialogSetProjects = true;
    },
    // 关闭配置项目弹窗
    closeUserSetDialog() {
      this.dialogSetProjects = false;
      this.useProjectlist = [];
    },

    // 获取选择的项目ID
    getSelectProjects(value) {
      this.projectIds = value;
      // console.log(value);
    },
    // 配置用户的所属项目
    // setUserProjects() {
    //   this.user.projectList = [];
    //   for (let id of this.projectIds) {
    //     this.projectList.forEach((item) => {
    //       if (id === item.projectId) {
    //         this.user.projectList.push(item);
    //       }
    //     });
    //   }
    //   // await updateProject(this.user.userId, this.user);
    //   // console.log(this.user.userId, this.user);
    //   for (let index = 0; index < this.usersList.length; index++) {
    //     if (this.usersList[index].userId === this.user.userId) {
    //       this.usersList.splice(index, 1, Object.assign({}, this.user));
    //       break;
    //     }
    //   }
    //   this.$message({
    //     type: "success",
    //     message: "配置项目成功",
    //   });
    //   this.dialogSetProjects = false;
    // },
    // closeSetProject() {
    //   this.checkAllProject = false;
    //   this.dialogSetProjects = false;
    // },

    // 获取用户角色选项
    async getRoles() {
      const { code, roleList } = await getRoles();
      if (code === 200) {
        this.rolesList = roleList;
      }
    },

    // 新增用户
    handleAddUser() {
      this.user = Object.assign({}, defaultUser);
      this.dialogType = "new";
      this.imageUrl = ""; //上传图片默认为空
      this.getRoles();
      this.dialogVisible = true;
    },
    // 编辑用户
    handleEdit(row) {
      this.dialogType = "edit";
      this.user = {};
      this.user = deepClone(row);
      this.imageUrl = `${this.baseUrl}/media/avatar/${row.userId
        }.png?time=${Date.now()}`;
      this.getRoles();
      this.dialogVisible = true;
    },
    // 关闭弹窗
    closeUserDialog() {
      this.user = Object.assign({}, defaultUser);
      this.projectIds = [];
      this.$refs.userForm.resetFields();
      this.dialogVisible = false;
    },
    // 新增/编辑弹窗确认按钮
    confirmUser() {
      this.user = deepClone(this.user);
      const isEdit = this.dialogType === "edit";
      this.$refs.userForm.validate(async (valid) => {
        if (valid) {
          if (isEdit) {
            let params = {
              userId: this.user.userId,
              userName: this.user.userName,
              nickName: this.user.nickName,
              roleId: this.user.roleId,
              projectList: this.user.projectList,
              phone: this.user.phone,
              email: this.user.email,
              isEnabled: this.user.isEnabled,
            };
            const { code, msg } = await editUser(params);
            if (code === 200) {
              this.uploadAvatar(this.uploadFile, params.userId);
              this.$message({
                type: "success",
                message: this.$t("permissionUser.editSuccessful"),
              });
              this.getUsersList();
              this.closeUserDialog();

              return;
            }
            if (code === 206) {
              this.$message({
                type: "error",
                message: this.$t("permissionUser.userNameExists"),
              });
              this.$refs.userName.focus();
            }
          } else {
            const { code, msg, data } = await createUser(this.user);
            if (code === 200) {
              this.uploadAvatar(this.uploadFile, data.userId);
              this.$message({
                type: "success",
                message: this.$t("permissionUser.addSuccessful"),
              });
              this.getUsersList();
              this.closeUserDialog();
              return;
            }
            if (code === 206) {
              this.$message({
                type: "error",
                message: this.$t("permissionUser.userNameExists"),
              });
              this.$refs.userName.focus();
            }
          }
        } else {
          // console.log("error submit!!");
          this.$message.warning(this.$t("permissionUser.RequiredTips"));
        }
      });
    },
    // 删除用户
    handleDelete(row) {
      this.$confirm(
        this.$t("permissionUser.isDeleteUser"),
        this.$t("permissionUser.tips"),
        {
          type: "warning",
        }
      )
        .then(async () => {
          let { code, msg } = await deleteUser({ userId: row.userId });
          if (code === 200) {
            this.getUsersList();
            this.$message({
              type: "success",
              message: this.$t("permissionUser.deleteSuccessful"),
            });
          }
        })
        .catch((err) => {
          console.error(err);
        });
    },
    // 单独设置是否启用该用户
    async changeSwitch(value, userId) {
      let params = {
        userId,
        isEnabled: value,
      };
      const { code, msg } = await isEnabledUser(params);
      if (code === 200) {
        this.$message({
          type: "success",
          message: this.$t("permissionUser.editSuccessful"),
        });
      }
    },

    // 获取上传头像的url
    fileChang(file, fileList) {
      // 上传头像前检查格式
      const isJPG =
        file.raw.type === "image/jpeg" ||
        file.raw.type === "image/png" ||
        file.raw.type === "image/webp" ||
        file.raw.type === "image/gif";
      const isLt2M = file.raw.size / 1024 / 1024 < 2;
      if (!isJPG) {
        this.$message.error(this.$t("permissionUser.isJPG"));
      }
      if (!isLt2M) {
        this.$message.error(this.$t("permissionUser.isLt2M"));
      }
      if (!isJPG || !isLt2M) return;
      this.imageUrl = URL.createObjectURL(file.raw);
      this.uploadFile = file.raw;
    },
    // 上传头像
    async uploadAvatar(uploadFile, userId) {
      if (!uploadFile) return;
      const form = new FormData(); // FormData 对象
      form.append("photo", uploadFile);
      form.append("userId", userId);
      let { code, msg } = await uploadAvatar(form);
      if (code !== 200) this.$message.error(msg);
      this.currentTime = Date.now();
      this.uploadFile = undefined;
    },

    // 重置用户密码
    resetPassword(userId) {
      this.$confirm(
        this.$t("permissionUser.isResetPwd"),
        this.$t("permissionUser.tips"),
        {
          type: "warning",
        }
      )
        .then(async () => {
          let { code, msg } = await updatePassword({
            userId,
            password: "111111",
          });
          if (code === 200) {
            this.$message({
              type: "success",
              message: this.$t("permissionUser.resetSuccessful"),
            });
          } else {
            this.$message({
              type: "error",
              message: msg,
            });
          }
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: this.$t("permissionUser.cancelReset"),
          });
        });
    },
  },
};
</script>

<style lang="scss" scoped>
// 修改elementui表格的默认样式
::v-deep .el-table__body-wrapper {
  cursor: pointer;

  &::-webkit-scrollbar {
    height: 6px;
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    // 轨道颜色
    background-color: #fff;
  }

  &::-webkit-scrollbar-thumb {
    // 滚动块颜色
    background-color: #e6e9ed;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    // hover高亮
    background-color: #d5d8db;
  }
}

// 当表格没有滚动条时
::v-deep .el-table__body-wrapper.is-scrolling-none~.el-table__fixed-right {
  height: 100% !important;
  bottom: 0 !important;
  box-shadow: none !important;
}

// 当表格有纵向滚动条时
::v-deep .el-table--scrollable-y .el-table__fixed-right {
  right: 6px !important;
}

// 当表格只有横向滚动条，没有纵向滚动条时
::v-deep .el-table--scrollable-x:not(.el-table--scrollable-y) .el-table__fixed-right {
  right: 0 !important;
}

// 处理有x滚动条时高度遮挡滚动条
::v-deep .el-table--scrollable-x {

  .el-table__fixed,
  .el-table__fixed-right {
    height: calc(100% - 6px) !important;
  }
}

// 存在滚动条的时候在right
::v-deep .el-table--scrollable-y {
  .el-table__fixed-right {
    right: 6px !important;
  }
}

// 解决修改滚动条底部不对齐问题
::v-deep .el-scrollbar__wrap::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.avatar-uploader ::v-deep.el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader ::v-deep.el-upload:hover {
  border-color: #409eff;

  .upload-handle {
    opacity: 1;
  }
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.avatar {
  width: 100px;
  height: 100px;
  display: block;
}

.upload-handle {
  position: absolute;
  top: 0;
  right: 0;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  cursor: pointer;
  background: rgb(0 0 0 / 60%);
  opacity: 0;
  transition: var(--el-transition-duration-fast);

  .handle-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 6%;
    color: aliceblue;

    .el-icon {
      margin-bottom: 40%;
      font-size: 130%;
      line-height: 130%;
    }

    span {
      font-size: 85%;
      line-height: 85%;
    }
  }
}
</style>
